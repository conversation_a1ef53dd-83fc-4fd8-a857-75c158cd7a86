import {useState} from 'react';
import useCodeVerification from './use-code-verification';
import {getServerErrorWarning} from '../utils/warnings/general-warning';
import {getEmailSchema} from '../validation/schemas/auth/email';
import {sendResetPasswordEmail} from '../services/reset-password/email-submition';
import {resetPassword} from '../services/reset-password/password-submition';
import {useAppNavigation} from '../../../hooks';

export default function useResetPassword() {
  const [step, setStep] = useState<'email' | 'code' | 'password'>('email');
  const [email, setEmail] = useState('');
  const [startPasswordStep, setStartPasswordStep] = useState(false);
  const [warning, setWarning] = useState({
    email: '',
    generalWarning: '',
  });
  const [passwordWarning, setPasswordWarning] = useState({
    password: '',
    confirmationPassword: '',
    generalWarning: '',
  });
  // React Native password state management
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const {verifyCode, displayedTimer, startTimer, code, setCode} =
    useCodeVerification();
  const [isLoading, setIsLoading] = useState(false);
  const navigation = useAppNavigation();

  function submitEmail() {
    //email verification
    const emailSchema = getEmailSchema();
    const verificationResult = emailSchema.safeParse({email});

    if (!verificationResult.success)
      setWarning({
        email: verificationResult.error.message,
        generalWarning: verificationResult.error.message,
      });
    else {
      setIsLoading(true);
      //submit email on the server side
      sendResetPasswordEmail(email).then((res) => {
        if (!res.ok) {
          setWarning({
            email: '',
            generalWarning: getServerErrorWarning(res.status),
          });
        } else {
          setStep('code');
          startTimer();

          if (warning.email !== '' || warning.generalWarning !== '')
            setWarning({
              email: '',
              generalWarning: '',
            });
        }

        setIsLoading(false);
      });
    }
  }

  function submitCode() {
    setIsLoading(true);
    if (!email) {
      setWarning({
        email: '',
        generalWarning: 'Email is required for code verification',
      });
      setIsLoading(false);
      return;
    }

    verifyCode(email, code).then((res) => {
      if (res.verified) {
        setStep('password');
        setTimeout(() => {
          setStartPasswordStep(true);
        }, 500);
      } else
        setWarning({
          email: '',
          generalWarning: res.message,
        });

      setIsLoading(false);
    });
  }

  function submitPassword() {
    const passwordVerification = verifyPasswordsRN();

    if (!passwordVerification.ok)
      setPasswordWarning(passwordVerification.warning);
    else {
      setIsLoading(true);

      //submit verified password on server side
      resetPassword({
        email,
        code,
        password: passwordVerification.password,
      }).then((res) => {
        setIsLoading(false);

        if (res.ok) navigation.navigate('TabNavigator');
        else
          setPasswordWarning({
            password: '',
            confirmationPassword: '',
            generalWarning: getServerErrorWarning(res.status),
          });
      });
    }
  }

  function verifyPasswordsRN() {
    if (password.length < 8) {
      return {
        ok: false,
        password: '',
        warning: {
          password: 'Le mot de passe doit contenir au moins 8 caractères',
          confirmationPassword: '',
          generalWarning: '',
        },
      };
    }

    if (confirmPassword.length < 8) {
      return {
        ok: false,
        password: '',
        warning: {
          password: '',
          confirmationPassword:
            'Le mot de passe doit contenir au moins 8 caractères',
          generalWarning: '',
        },
      };
    }

    if (password !== confirmPassword) {
      return {
        ok: false,
        password: '',
        warning: {
          password: '',
          confirmationPassword: 'Les mots de passe ne correspondent pas',
          generalWarning: 'Les mots de passe ne correspondent pas',
        },
      };
    }

    return {
      ok: true,
      password: password,
      warning: {
        password: '',
        confirmationPassword: '',
        generalWarning: '',
      },
    };
  }

  return {
    step,
    warning,
    passwordWarning,
    submitEmail,
    displayedTimer,
    submitCode,
    code,
    setCode,
    email,
    setEmail,
    submitPassword,
    startPasswordStep,
    isLoading,
    setStep,
    // React Native password state
    password,
    setPassword,
    confirmPassword,
    setConfirmPassword,
  };
}
